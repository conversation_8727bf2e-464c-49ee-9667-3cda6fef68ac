// Verificar se o bilhete existe no banco de dados
const mysql = require('mysql2/promise');

async function checkBilheteInDatabase() {
  console.log('🔍 Verificando bilhete no banco de dados...\n');

  // Dados do bilhete
  const bilheteCodigo = 'BLT175262885945121WAZV11';
  const transactionId = 'pixi_01k08d427xfkqb4j48da01qt8r';
  const userId = 21;

  console.log('🎫 Dados para busca:');
  console.log(`  - Código: ${bilheteCodigo}`);
  console.log(`  - Transaction ID: ${transactionId}`);
  console.log(`  - User ID: ${userId}\n`);

  // Configuração do banco (mesma do sistema)
  const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'sistema-bolao-top',
    port: 3306
  };

  let connection;

  try {
    console.log('🔌 Conectando ao banco de dados...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Conectado ao MySQL!\n');

    // 1. Buscar bilhete por código
    console.log('1️⃣ Buscando bilhete por CÓDIGO:');
    const [bilhetesPorCodigo] = await connection.execute(`
      SELECT id, codigo, status, transaction_id, valor, user_id, created_at, updated_at
      FROM bilhetes 
      WHERE codigo = ?
    `, [bilheteCodigo]);

    if (bilhetesPorCodigo.length > 0) {
      console.log('✅ Bilhete encontrado por código:');
      bilhetesPorCodigo.forEach(bilhete => {
        console.log(`  - ID: ${bilhete.id}`);
        console.log(`  - Código: ${bilhete.codigo}`);
        console.log(`  - Status: ${bilhete.status}`);
        console.log(`  - Transaction ID: ${bilhete.transaction_id}`);
        console.log(`  - Valor: R$ ${bilhete.valor}`);
        console.log(`  - User ID: ${bilhete.user_id}`);
        console.log(`  - Criado em: ${bilhete.created_at}`);
        console.log(`  - Atualizado em: ${bilhete.updated_at}`);
      });
    } else {
      console.log('❌ Nenhum bilhete encontrado por código');
    }

    // 2. Buscar bilhete por transaction_id
    console.log('\n2️⃣ Buscando bilhete por TRANSACTION_ID:');
    const [bilhetesPorTransaction] = await connection.execute(`
      SELECT id, codigo, status, transaction_id, valor, user_id, created_at, updated_at
      FROM bilhetes 
      WHERE transaction_id = ?
    `, [transactionId]);

    if (bilhetesPorTransaction.length > 0) {
      console.log('✅ Bilhete encontrado por transaction_id:');
      bilhetesPorTransaction.forEach(bilhete => {
        console.log(`  - ID: ${bilhete.id}`);
        console.log(`  - Código: ${bilhete.codigo}`);
        console.log(`  - Status: ${bilhete.status}`);
        console.log(`  - Transaction ID: ${bilhete.transaction_id}`);
        console.log(`  - Valor: R$ ${bilhete.valor}`);
        console.log(`  - User ID: ${bilhete.user_id}`);
        console.log(`  - Criado em: ${bilhete.created_at}`);
        console.log(`  - Atualizado em: ${bilhete.updated_at}`);
      });
    } else {
      console.log('❌ Nenhum bilhete encontrado por transaction_id');
    }

    // 3. Buscar bilhetes do usuário 21
    console.log('\n3️⃣ Buscando TODOS os bilhetes do usuário 21:');
    const [bilhetesDoUsuario] = await connection.execute(`
      SELECT id, codigo, status, transaction_id, valor, user_id, created_at, updated_at
      FROM bilhetes 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `, [userId]);

    if (bilhetesDoUsuario.length > 0) {
      console.log(`✅ ${bilhetesDoUsuario.length} bilhetes encontrados para o usuário:');
      bilhetesDoUsuario.forEach((bilhete, index) => {
        console.log(`\n  📋 Bilhete ${index + 1}:`);
        console.log(`    - ID: ${bilhete.id}`);
        console.log(`    - Código: ${bilhete.codigo}`);
        console.log(`    - Status: ${bilhete.status}`);
        console.log(`    - Transaction ID: ${bilhete.transaction_id || 'NULL'}`);
        console.log(`    - Valor: R$ ${bilhete.valor}`);
        console.log(`    - Criado em: ${bilhete.created_at}`);
        
        // Verificar se é o bilhete que estamos procurando
        if (bilhete.codigo === bilheteCodigo) {
          console.log('    🎯 ESTE É O BILHETE QUE ESTAMOS PROCURANDO!');
        }
        if (bilhete.transaction_id === transactionId) {
          console.log('    🎯 TRANSACTION_ID CORRESPONDE!');
        }
      });
    } else {
      console.log('❌ Nenhum bilhete encontrado para o usuário');
    }

    // 4. Verificar estrutura da tabela
    console.log('\n4️⃣ Verificando estrutura da tabela bilhetes:');
    const [colunas] = await connection.execute(`
      DESCRIBE bilhetes
    `);

    console.log('📋 Colunas da tabela bilhetes:');
    colunas.forEach(coluna => {
      console.log(`  - ${coluna.Field}: ${coluna.Type} (${coluna.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    // 5. Buscar com query similar ao webhook
    console.log('\n5️⃣ Testando query EXATA do webhook:');
    const [webhookQuery] = await connection.execute(`
      SELECT id, codigo, status, transaction_id FROM bilhetes
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [bilheteCodigo, bilheteCodigo]);

    if (webhookQuery.length > 0) {
      console.log('✅ Query do webhook encontrou bilhete:');
      const bilhete = webhookQuery[0];
      console.log(`  - ID: ${bilhete.id}`);
      console.log(`  - Código: ${bilhete.codigo}`);
      console.log(`  - Status: ${bilhete.status}`);
      console.log(`  - Transaction ID: ${bilhete.transaction_id}`);
    } else {
      console.log('❌ Query do webhook NÃO encontrou bilhete');
    }

    // 6. Testar com transaction_id
    console.log('\n6️⃣ Testando query do webhook com transaction_id:');
    const [webhookQuery2] = await connection.execute(`
      SELECT id, codigo, status, transaction_id FROM bilhetes
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [transactionId, transactionId]);

    if (webhookQuery2.length > 0) {
      console.log('✅ Query do webhook com transaction_id encontrou bilhete:');
      const bilhete = webhookQuery2[0];
      console.log(`  - ID: ${bilhete.id}`);
      console.log(`  - Código: ${bilhete.codigo}`);
      console.log(`  - Status: ${bilhete.status}`);
      console.log(`  - Transaction ID: ${bilhete.transaction_id}`);
    } else {
      console.log('❌ Query do webhook com transaction_id NÃO encontrou bilhete');
    }

  } catch (error) {
    console.error('❌ Erro ao conectar/consultar banco:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Dicas para resolver:');
      console.log('   1. Verifique se o MySQL está rodando');
      console.log('   2. Verifique usuário/senha do banco');
      console.log('   3. Verifique se o banco "sistema-bolao-top" existe');
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Conexão fechada');
    }
  }

  console.log('\n🎯 VERIFICAÇÃO CONCLUÍDA!');
  console.log('📋 Se o bilhete foi encontrado, o webhook deveria funcionar.');
  console.log('📋 Se não foi encontrado, há um problema na criação/armazenamento do bilhete.');
}

// Executar verificação
checkBilheteInDatabase().catch(console.error);
