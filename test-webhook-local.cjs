// Testar webhook localmente
const http = require('http');

function makeLocalRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWebhookLocal() {
  console.log('🧪 Testando webhook LOCALMENTE...\n');

  // Dados do bilhete que está pendente
  const bilheteCodigo = 'BLT175262885945121WAZV11';
  const transactionId = 'pixi_01k08d427xfkqb4j48da01qt8r';
  
  console.log('🎫 Bilhete para teste:');
  console.log(`  - Código: ${bilheteCodigo}`);
  console.log(`  - Transaction ID: ${transactionId}`);
  console.log(`  - Status atual: Pendente\n`);

  // Aguardar servidor local estar rodando
  console.log('⏳ Aguardando servidor local estar disponível...');
  
  let serverReady = false;
  let attempts = 0;
  const maxAttempts = 10;
  
  while (!serverReady && attempts < maxAttempts) {
    try {
      const testOptions = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/health',
        method: 'GET',
        timeout: 2000
      };
      
      await makeLocalRequest(testOptions);
      serverReady = true;
      console.log('✅ Servidor local está rodando!\n');
    } catch (error) {
      attempts++;
      console.log(`⏳ Tentativa ${attempts}/${maxAttempts} - Aguardando servidor...`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  if (!serverReady) {
    console.log('❌ Servidor local não está disponível!');
    console.log('💡 Execute: npm run dev');
    return;
  }

  // 1. Testar webhook do Mercado Pago com código do bilhete
  console.log('1️⃣ Testando webhook MP LOCAL com código do bilhete:');
  try {
    const webhookOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/MP/webhookruntransation',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MercadoPago-Webhook/1.0'
      }
    };

    const webhookData = {
      order_id: bilheteCodigo,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment processed successfully - LOCAL TEST'
    };

    console.log('📤 Enviando webhook...');
    console.log(`  - order_id: ${webhookData.order_id}`);
    console.log(`  - status: ${webhookData.status}`);
    
    const webhookResult = await makeLocalRequest(webhookOptions, webhookData);
    console.log(`✅ Status HTTP: ${webhookResult.status}`);
    
    if (webhookResult.status === 200) {
      console.log('🎉 Webhook processado com sucesso:');
      console.log(JSON.stringify(webhookResult.data, null, 2));
      
      if (webhookResult.data.status === 'pago') {
        console.log('\n🎊 SUCESSO! Bilhete marcado como PAGO!');
        console.log('🔔 A notificação deve aparecer no site agora!');
        console.log('🌐 Acesse http://localhost:3000/ para ver a notificação!');
      } else {
        console.log('\n⚠️ Webhook processado mas bilhete não foi marcado como pago');
        console.log(`Status retornado: ${webhookResult.data.status}`);
      }
    } else {
      console.log('❌ Erro no webhook:', webhookResult.data);
    }
  } catch (error) {
    console.log('❌ Erro ao processar webhook:', error.message);
  }

  // 2. Se não funcionou com código, testar com transaction_id
  console.log('\n2️⃣ Testando webhook MP LOCAL com transaction_id:');
  try {
    const webhookOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/MP/webhookruntransation',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MercadoPago-Webhook/1.0'
      }
    };

    const webhookData = {
      order_id: transactionId,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment processed successfully - LOCAL TEST WITH TRANSACTION_ID'
    };

    console.log('📤 Enviando webhook...');
    console.log(`  - order_id: ${webhookData.order_id}`);
    console.log(`  - status: ${webhookData.status}`);
    
    const webhookResult = await makeLocalRequest(webhookOptions, webhookData);
    console.log(`✅ Status HTTP: ${webhookResult.status}`);
    
    if (webhookResult.status === 200) {
      console.log('🎉 Webhook processado com sucesso:');
      console.log(JSON.stringify(webhookResult.data, null, 2));
      
      if (webhookResult.data.status === 'pago') {
        console.log('\n🎊 SUCESSO! Bilhete marcado como PAGO!');
        console.log('🔔 A notificação deve aparecer no site agora!');
      }
    } else {
      console.log('❌ Erro no webhook:', webhookResult.data);
    }
  } catch (error) {
    console.log('❌ Erro ao processar webhook:', error.message);
  }

  // 3. Verificar status final
  console.log('\n3️⃣ Verificando status final do bilhete:');
  try {
    const statusOptions = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/user/bilhetes?user_id=21`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const statusResult = await makeLocalRequest(statusOptions);
    console.log(`✅ Status HTTP: ${statusResult.status}`);
    
    if (statusResult.status === 200) {
      console.log('🎫 Bilhetes do usuário:');
      const bilhetes = statusResult.data.bilhetes || [];
      const bilheteTestado = bilhetes.find(b => b.codigo === bilheteCodigo);
      
      if (bilheteTestado) {
        console.log(`  - Código: ${bilheteTestado.codigo}`);
        console.log(`  - Status: ${bilheteTestado.status}`);
        console.log(`  - Valor: R$ ${bilheteTestado.valor}`);
        
        if (bilheteTestado.status === 'pago') {
          console.log('\n🎉 CONFIRMADO! O bilhete está marcado como PAGO!');
          console.log('✅ A notificação de sucesso deve estar funcionando!');
        } else {
          console.log('\n⚠️ Bilhete ainda não está marcado como pago.');
          console.log('💡 Tente usar o botão "Já Paguei - Confirmar Manualmente" no site.');
        }
      } else {
        console.log('❌ Bilhete não encontrado na lista');
      }
    } else {
      console.log('❌ Erro ao verificar status:', statusResult.data);
    }
  } catch (error) {
    console.log('❌ Erro ao verificar status:', error.message);
  }

  console.log('\n🎯 TESTE LOCAL CONCLUÍDO!');
  console.log('📋 Próximos passos:');
  console.log('   1. 🌐 Acesse http://localhost:3000/');
  console.log('   2. 🔑 Faça login com sua conta');
  console.log('   3. 📋 Vá em "Meus Pagamentos"');
  console.log('   4. 🔘 Se ainda estiver pendente, use "Já Paguei - Confirmar Manualmente"');
  console.log('   5. 🎉 Veja a notificação de sucesso aparecer!');
}

// Executar teste
testWebhookLocal().catch(console.error);
