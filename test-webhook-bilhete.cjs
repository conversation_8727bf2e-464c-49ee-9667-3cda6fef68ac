// Testar webhook para bilhete específico
const https = require('https');

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWebhookBilhete() {
  console.log('🧪 Testando webhook para bilhete pendente...\n');

  // Dados do bilhete que está pendente
  const bilheteCodigo = 'BLT175262885945121WAZV11';
  const transactionId = 'pixi_01k08d427xfkqb4j48da01qt8r';
  const valor = 1.00;
  
  console.log('🎫 Bilhete para teste:');
  console.log(`  - Código: ${bilheteCodigo}`);
  console.log(`  - Transaction ID: ${transactionId}`);
  console.log(`  - Valor: R$ ${valor}`);
  console.log(`  - Status atual: Pendente\n`);

  // 1. Testar webhook do Mercado Pago com código do bilhete
  console.log('1️⃣ Testando webhook MP com código do bilhete:');
  try {
    const webhookOptions = {
      hostname: 'ouroemu.site',
      port: 443,
      path: '/api/v1/MP/webhookruntransation',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MercadoPago-Webhook/1.0'
      }
    };

    const webhookData = {
      order_id: bilheteCodigo,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment processed successfully - MANUAL TEST'
    };

    console.log('📤 Enviando webhook...');
    console.log(`  - order_id: ${webhookData.order_id}`);
    console.log(`  - status: ${webhookData.status}`);
    
    const webhookResult = await makeRequest(webhookOptions, webhookData);
    console.log(`✅ Status HTTP: ${webhookResult.status}`);
    
    if (webhookResult.status === 200) {
      console.log('🎉 Webhook processado com sucesso:');
      console.log(`  - Status no banco: ${webhookResult.data.status}`);
      console.log(`  - Processado: ${webhookResult.data.processed_successfully ? 'Sim' : 'Não'}`);
      console.log(`  - Bilhete: ${webhookResult.data.bilhete_codigo}`);
      console.log(`  - Valor: R$ ${webhookResult.data.valor}`);
      
      if (webhookResult.data.status === 'pago') {
        console.log('\n🎊 SUCESSO! Bilhete marcado como PAGO!');
        console.log('🔔 A notificação deve aparecer no site agora!');
        console.log('🌐 Acesse https://ouroemu.site/ para ver a notificação!');
      } else {
        console.log('\n⚠️ Webhook processado mas bilhete não foi marcado como pago');
        console.log(`Status retornado: ${webhookResult.data.status}`);
      }
    } else {
      console.log('❌ Erro no webhook:', webhookResult.data);
    }
  } catch (error) {
    console.log('❌ Erro ao processar webhook:', error.message);
  }

  // 2. Se não funcionou com código, testar com transaction_id
  console.log('\n2️⃣ Testando webhook MP com transaction_id:');
  try {
    const webhookOptions = {
      hostname: 'ouroemu.site',
      port: 443,
      path: '/api/v1/MP/webhookruntransation',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MercadoPago-Webhook/1.0'
      }
    };

    const webhookData = {
      order_id: transactionId,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment processed successfully - MANUAL TEST WITH TRANSACTION_ID'
    };

    console.log('📤 Enviando webhook...');
    console.log(`  - order_id: ${webhookData.order_id}`);
    console.log(`  - status: ${webhookData.status}`);
    
    const webhookResult = await makeRequest(webhookOptions, webhookData);
    console.log(`✅ Status HTTP: ${webhookResult.status}`);
    
    if (webhookResult.status === 200) {
      console.log('🎉 Webhook processado com sucesso:');
      console.log(`  - Status no banco: ${webhookResult.data.status}`);
      console.log(`  - Processado: ${webhookResult.data.processed_successfully ? 'Sim' : 'Não'}`);
      console.log(`  - Bilhete: ${webhookResult.data.bilhete_codigo}`);
      
      if (webhookResult.data.status === 'pago') {
        console.log('\n🎊 SUCESSO! Bilhete marcado como PAGO!');
        console.log('🔔 A notificação deve aparecer no site agora!');
      }
    } else {
      console.log('❌ Erro no webhook:', webhookResult.data);
    }
  } catch (error) {
    console.log('❌ Erro ao processar webhook:', error.message);
  }

  // 3. Verificar status final
  console.log('\n3️⃣ Verificando status final do bilhete:');
  try {
    const statusOptions = {
      hostname: 'ouroemu.site',
      port: 443,
      path: `/api/webhook/status?order_id=${bilheteCodigo}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const statusResult = await makeRequest(statusOptions);
    console.log(`✅ Status HTTP: ${statusResult.status}`);
    
    if (statusResult.status === 200) {
      console.log('🎫 Status final do bilhete:');
      console.log(`  - Código: ${statusResult.data.bilhete?.codigo}`);
      console.log(`  - Status: ${statusResult.data.status}`);
      console.log(`  - Pago: ${statusResult.data.payment_info?.is_paid ? 'Sim' : 'Não'}`);
      console.log(`  - Valor: R$ ${statusResult.data.bilhete?.valor}`);
      
      if (statusResult.data.payment_info?.is_paid) {
        console.log('\n🎉 CONFIRMADO! O bilhete está marcado como PAGO!');
        console.log('✅ A notificação de sucesso deve estar funcionando!');
      } else {
        console.log('\n⚠️ Bilhete ainda não está marcado como pago.');
        console.log('💡 Tente usar o botão "Já Paguei - Confirmar Manualmente" no site.');
      }
    } else {
      console.log('❌ Erro ao verificar status:', statusResult.data);
    }
  } catch (error) {
    console.log('❌ Erro ao verificar status:', error.message);
  }

  console.log('\n🎯 TESTE CONCLUÍDO!');
  console.log('📋 Próximos passos:');
  console.log('   1. 🌐 Acesse https://ouroemu.site/');
  console.log('   2. 🔑 Faça login com sua conta');
  console.log('   3. 📋 Vá em "Meus Pagamentos"');
  console.log('   4. 🔘 Se ainda estiver pendente, use "Já Paguei - Confirmar Manualmente"');
  console.log('   5. 🎉 Veja a notificação de sucesso aparecer!');
}

// Executar teste
testWebhookBilhete().catch(console.error);
