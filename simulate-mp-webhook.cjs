// Simular webhook do Mercado Pago
const mysql = require('mysql2/promise');

async function simulateMPWebhook() {
  console.log('🔔 Simulando webhook do Mercado Pago...\n');

  // Dados do bilhete real criado (do log)
  const realTransactionId = 'pixi_01k08dvngbfn8srf484ae00tyt';
  const realBilheteCodigo = 'BLT175262963365921J4FGQD';

  console.log('🎫 Dados do bilhete real:');
  console.log(`  - Transaction ID: ${realTransactionId}`);
  console.log(`  - Código: ${realBilheteCodigo}`);

  // Configuração do banco
  const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'sistema-bolao-top',
    port: 3306
  };

  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Conectado ao banco de dados\n');

    // 1. <PERSON><PERSON>, criar um bilhete de teste com os dados reais
    console.log('1️⃣ Criando bilhete de teste com dados reais...');
    
    const testBilhete = {
      codigo: realBilheteCodigo,
      usuario_id: 1, // Administrador
      usuario_nome: 'mma mma',
      usuario_email: '<EMAIL>',
      usuario_cpf: '995.445.230-30',
      valor_total: 1.00,
      quantidade_apostas: 11,
      status: 'pendente',
      qr_code_pix: '00020126810014br.gov.bcb.pix2559qr-code.picpay.com/pix/655dfd64-3940-4b44-b667-5b9e70f9bd725204000053039865802BR5916DLM TECNOLOGIA E6009Sao Paulo62070503***6304A01C',
      transaction_id: realTransactionId
    };

    // Verificar se já existe
    const [existingBilhete] = await connection.execute(`
      SELECT id, codigo, status, transaction_id FROM bilhetes
      WHERE transaction_id = ? OR codigo = ?
    `, [realTransactionId, realBilheteCodigo]);

    if (existingBilhete.length === 0) {
      // Inserir bilhete de teste
      const [insertResult] = await connection.execute(`
        INSERT INTO bilhetes (
          codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
          valor_total, quantidade_apostas, status, qr_code_pix, transaction_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testBilhete.codigo,
        testBilhete.usuario_id,
        testBilhete.usuario_nome,
        testBilhete.usuario_email,
        testBilhete.usuario_cpf,
        testBilhete.valor_total,
        testBilhete.quantidade_apostas,
        testBilhete.status,
        testBilhete.qr_code_pix,
        testBilhete.transaction_id
      ]);

      console.log('✅ Bilhete de teste criado com sucesso!');
      console.log(`  - ID: ${insertResult.insertId}`);
    } else {
      console.log('✅ Bilhete já existe no banco:');
      console.log(`  - ID: ${existingBilhete[0].id}`);
      console.log(`  - Status atual: ${existingBilhete[0].status}`);
    }

    // 2. Simular webhook do Mercado Pago
    console.log('\n2️⃣ Simulando webhook do Mercado Pago...');
    
    const webhookData = {
      order_id: realTransactionId,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment confirmed'
    };

    console.log('📨 Dados do webhook:');
    console.log(`  - order_id: ${webhookData.order_id}`);
    console.log(`  - status: ${webhookData.status}`);
    console.log(`  - type: ${webhookData.type}`);

    // 3. Executar lógica do webhook
    console.log('\n3️⃣ Executando lógica do webhook...');

    // Mapear status
    let dbStatus = webhookData.status.toLowerCase();
    if (webhookData.status === 'PAID' || webhookData.status === 'paid' || webhookData.status === 'APROVADO' || webhookData.status === 'aprovado') {
      dbStatus = 'pago';
    }

    console.log(`📊 Status mapeado: ${webhookData.status} -> ${dbStatus}`);

    // Buscar bilhete antes da atualização
    const [bilheteAntes] = await connection.execute(`
      SELECT id, codigo, status, transaction_id FROM bilhetes
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [webhookData.order_id, webhookData.order_id]);

    console.log('🔍 Bilhete antes da atualização:', bilheteAntes[0] || 'Não encontrado');

    // Atualizar bilhete
    const [updateResult] = await connection.execute(`
      UPDATE bilhetes
      SET status = ?, updated_at = NOW()
      WHERE transaction_id = ? OR codigo = ?
    `, [dbStatus, webhookData.order_id, webhookData.order_id]);

    console.log('✅ Resultado da atualização:');
    console.log(`  - Linhas afetadas: ${updateResult.affectedRows}`);
    console.log(`  - Linhas alteradas: ${updateResult.changedRows}`);

    // Buscar bilhete atualizado
    const [bilheteAtualizado] = await connection.execute(`
      SELECT * FROM bilhetes 
      WHERE transaction_id = ? OR codigo = ? 
      LIMIT 1
    `, [webhookData.order_id, webhookData.order_id]);

    if (bilheteAtualizado.length > 0) {
      const bilhete = bilheteAtualizado[0];
      console.log('\n✅ Bilhete encontrado e atualizado:');
      console.log(`  - ID: ${bilhete.id}`);
      console.log(`  - Código: ${bilhete.codigo}`);
      console.log(`  - Status: ${bilhete.status} ${bilhete.status === 'pago' ? '✅' : '❌'}`);
      console.log(`  - Transaction ID: ${bilhete.transaction_id}`);
      console.log(`  - Valor: R$ ${bilhete.valor_total}`);
      console.log(`  - Usuário: ${bilhete.usuario_nome}`);
      console.log(`  - Atualizado em: ${bilhete.updated_at}`);

      // Simular resposta do webhook
      const webhookResponse = {
        message: "Webhook Mercado Pago processado com sucesso",
        timestamp: new Date().toISOString(),
        status: dbStatus,
        order_id: webhookData.order_id,
        payment_status: webhookData.status,
        type: webhookData.type,
        bilhete_codigo: bilhete.codigo,
        bilhete_id: bilhete.id,
        valor: parseFloat(bilhete.valor_total),
        status_anterior: bilheteAntes[0]?.status || 'desconhecido',
        processed_successfully: true
      };

      console.log('\n📤 Resposta do webhook:');
      console.log(JSON.stringify(webhookResponse, null, 2));

      console.log('\n🎉 WEBHOOK SIMULADO COM SUCESSO!');
      console.log('✅ O pagamento foi confirmado');
      console.log('✅ O status do bilhete foi atualizado para "pago"');
      console.log('✅ O webhook retornou resposta de sucesso');

    } else {
      console.log('❌ Bilhete não encontrado após atualização');
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Conexão fechada');
    }
  }
}

// Executar simulação
simulateMPWebhook().catch(console.error);
