// Verificar usuários existentes
const mysql = require('mysql2/promise');

async function checkUsers() {
  console.log('👥 Verificando usuários existentes...\n');

  // Configuração do banco
  const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'sistema-bolao-top',
    port: 3306
  };

  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Conectado ao banco de dados\n');

    // Listar usuários
    const [usuarios] = await connection.execute(`
      SELECT id, nome, email
      FROM usuarios
      ORDER BY id
    `);

    console.log(`📋 ${usuarios.length} usuários encontrados:`);
    usuarios.forEach((usuario, index) => {
      console.log(`  ${index + 1}. ID: ${usuario.id} - ${usuario.nome} - ${usuario.email}`);
    });

    // Verificar bilhetes por usuário
    console.log('\n📊 Bilhetes por usuário:');
    const [bilhetesPorUsuario] = await connection.execute(`
      SELECT usuario_id, COUNT(*) as count, SUM(valor_total) as total_valor
      FROM bilhetes 
      GROUP BY usuario_id
      ORDER BY usuario_id
    `);

    bilhetesPorUsuario.forEach(stat => {
      const usuario = usuarios.find(u => u.id === stat.usuario_id);
      const nomeUsuario = usuario ? usuario.nome : 'Usuário não encontrado';
      console.log(`  - ID ${stat.usuario_id} (${nomeUsuario}): ${stat.count} bilhetes (R$ ${parseFloat(stat.total_valor || 0).toFixed(2)})`);
    });

  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Conexão fechada');
    }
  }
}

// Executar verificação
checkUsers().catch(console.error);
