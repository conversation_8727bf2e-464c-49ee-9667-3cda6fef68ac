// Verificar estrutura do banco de dados
const mysql = require('mysql2/promise');

async function checkDatabaseStructure() {
  console.log('🔍 Verificando estrutura do banco de dados...\n');

  // Configuração do banco
  const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'sistema-bolao-top',
    port: 3306
  };

  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Conectado ao banco de dados\n');

    // 1. Verificar estrutura da tabela bilhetes
    console.log('1️⃣ Estrutura da tabela bilhetes:');
    const [bilhetesStructure] = await connection.execute('DESCRIBE bilhetes');
    
    console.log('📋 Colunas da tabela bilhetes:');
    bilhetesStructure.forEach((column, index) => {
      console.log(`  ${index + 1}. ${column.Field} - ${column.Type} - ${column.Null} - ${column.Key} - ${column.Default}`);
    });

    // 2. Verificar se existe coluna transaction_id
    const hasTransactionId = bilhetesStructure.some(col => col.Field === 'transaction_id');
    console.log(`\n🔍 Coluna transaction_id existe: ${hasTransactionId ? '✅ SIM' : '❌ NÃO'}`);

    // 3. Contar bilhetes por status
    console.log('\n2️⃣ Estatísticas dos bilhetes:');
    const [statusStats] = await connection.execute(`
      SELECT status, COUNT(*) as count, SUM(valor_total) as total_valor
      FROM bilhetes 
      GROUP BY status
    `);

    console.log('📊 Bilhetes por status:');
    statusStats.forEach(stat => {
      console.log(`  - ${stat.status}: ${stat.count} bilhetes (R$ ${parseFloat(stat.total_valor || 0).toFixed(2)})`);
    });

    // 4. Verificar bilhetes com transaction_id
    console.log('\n3️⃣ Bilhetes com transaction_id:');
    const [bilhetesComTransaction] = await connection.execute(`
      SELECT id, codigo, status, transaction_id, valor_total, usuario_nome, created_at
      FROM bilhetes 
      WHERE transaction_id IS NOT NULL AND transaction_id != ''
      ORDER BY created_at DESC
      LIMIT 10
    `);

    if (bilhetesComTransaction.length > 0) {
      console.log(`✅ ${bilhetesComTransaction.length} bilhetes com transaction_id:`);
      bilhetesComTransaction.forEach((bilhete, index) => {
        console.log(`  ${index + 1}. ${bilhete.codigo} - ${bilhete.status} - ${bilhete.transaction_id}`);
      });
    } else {
      console.log('❌ Nenhum bilhete com transaction_id encontrado');
    }

    // 5. Verificar outras tabelas relacionadas
    console.log('\n4️⃣ Verificando outras tabelas:');
    
    // Verificar se existe tabela pagamentos
    try {
      const [pagamentosStructure] = await connection.execute('DESCRIBE pagamentos');
      console.log('\n📋 Tabela pagamentos existe:');
      console.log('  Colunas:', pagamentosStructure.map(col => col.Field).join(', '));
      
      // Contar pagamentos
      const [pagamentosCount] = await connection.execute('SELECT COUNT(*) as count FROM pagamentos');
      console.log(`  Total de registros: ${pagamentosCount[0].count}`);
      
    } catch (error) {
      console.log('❌ Tabela pagamentos não existe ou erro:', error.message);
    }

    // 6. Verificar bilhetes mais recentes (últimas 24h)
    console.log('\n5️⃣ Bilhetes das últimas 24 horas:');
    const [bilhetesRecentes] = await connection.execute(`
      SELECT id, codigo, status, transaction_id, valor_total, usuario_nome, created_at
      FROM bilhetes 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      ORDER BY created_at DESC
    `);

    console.log(`📋 ${bilhetesRecentes.length} bilhetes nas últimas 24h:`);
    bilhetesRecentes.forEach((bilhete, index) => {
      console.log(`  ${index + 1}. ${bilhete.codigo} - ${bilhete.status} - R$ ${bilhete.valor_total}`);
      console.log(`     Transaction ID: ${bilhete.transaction_id || 'N/A'}`);
      console.log(`     Usuário: ${bilhete.usuario_nome}`);
      console.log(`     Criado: ${bilhete.created_at}`);
      console.log('');
    });

    // 7. Buscar por padrão do código do bilhete
    console.log('6️⃣ Buscando bilhetes com padrão similar:');
    const [bilhetesSimilares] = await connection.execute(`
      SELECT id, codigo, status, transaction_id, valor_total, usuario_nome, created_at
      FROM bilhetes 
      WHERE codigo LIKE 'BLT1752629%'
      ORDER BY created_at DESC
    `);

    if (bilhetesSimilares.length > 0) {
      console.log(`✅ ${bilhetesSimilares.length} bilhetes com padrão similar:`);
      bilhetesSimilares.forEach((bilhete, index) => {
        console.log(`  ${index + 1}. ${bilhete.codigo} - ${bilhete.status} - ${bilhete.transaction_id || 'N/A'}`);
      });
    } else {
      console.log('❌ Nenhum bilhete com padrão similar encontrado');
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Conexão fechada');
    }
  }
}

// Executar verificação
checkDatabaseStructure().catch(console.error);
